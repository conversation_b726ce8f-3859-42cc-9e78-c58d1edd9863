<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cumulative Delta Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1e1e1e;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .data-row {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #444;
        }
        .data-row:last-child {
            border-bottom: none;
        }
        .positive {
            color: #089981;
        }
        .negative {
            color: #F23645;
        }
        button {
            background-color: #089981;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0a7a6b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cumulative Delta Calculation Test</h1>
        
        <div class="test-section">
            <h2>Sample Data Structure</h2>
            <p>This demonstrates how cumulative delta is calculated from your candle data structure:</p>
            <pre id="sampleData"></pre>
        </div>

        <div class="test-section">
            <h2>Cumulative Delta Calculation</h2>
            <button onclick="runTest()">Run Cumulative Delta Test</button>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>Daily Reset Example</h2>
            <button onclick="runDailyResetTest()">Test Daily Reset</button>
            <div id="dailyResetResults"></div>
        </div>
    </div>

    <script>
        // Sample data structure based on your format
        const sampleCandles = [
            {
                time: 1752558600, // Day 1
                open: 25276,
                high: 25290.4,
                low: 25276,
                close: 25287.5,
                volume: 36450,
                buy_vol: 20475,
                sell_vol: 12675,
                delta: 7800,
                poc: 25284,
                footprint: [
                    {buyVolume: 2625, priceLevel: 25290, sellVolume: 0},
                    {buyVolume: 7200, priceLevel: 25285, sellVolume: 5025},
                    {buyVolume: 9900, priceLevel: 25280, sellVolume: 6075},
                    {buyVolume: 750, priceLevel: 25275, sellVolume: 1575}
                ]
            },
            {
                time: 1752558900, // Day 1 - 5 minutes later
                open: 25287.5,
                high: 25295,
                low: 25280,
                close: 25290,
                volume: 28500,
                buy_vol: 18000,
                sell_vol: 10500,
                delta: 7500,
                poc: 25290
            },
            {
                time: 1752645000, // Day 2 - next day
                open: 25290,
                high: 25300,
                low: 25285,
                close: 25295,
                volume: 32000,
                buy_vol: 15000,
                sell_vol: 17000,
                delta: -2000,
                poc: 25295
            }
        ];

        // Display sample data
        document.getElementById('sampleData').textContent = JSON.stringify(sampleCandles[0], null, 2);

        // Cumulative delta calculation function (from footprint.js)
        function calculateCumulativeDelta(candles) {
            if (!Array.isArray(candles) || candles.length === 0) return [];
            
            let cumulativeDelta = 0;
            let currentDay = null;
            
            // Sort candles by time
            const sortedCandles = [...candles].sort((a, b) => a.time - b.time);
            
            return sortedCandles.map(candle => {
                const candleDate = new Date(candle.time * 1000);
                const dayKey = candleDate.toDateString();
                
                // Reset cumulative delta at start of new trading day
                if (currentDay !== dayKey) {
                    currentDay = dayKey;
                    cumulativeDelta = 0;
                }
                
                // Add current candle's delta to cumulative
                cumulativeDelta += (candle.delta || 0);
                
                return {
                    ...candle,
                    cumulativeDelta: cumulativeDelta,
                    tradingDay: dayKey
                };
            });
        }

        function runTest() {
            const results = calculateCumulativeDelta(sampleCandles);
            
            let html = '<h3>Results:</h3>';
            results.forEach((candle, index) => {
                const deltaClass = candle.delta >= 0 ? 'positive' : 'negative';
                const cumDeltaClass = candle.cumulativeDelta >= 0 ? 'positive' : 'negative';
                
                html += `
                    <div class="data-row">
                        <span>Candle ${index + 1}</span>
                        <span>Time: ${new Date(candle.time * 1000).toLocaleString()}</span>
                        <span class="${deltaClass}">Δ: ${candle.delta}</span>
                        <span class="${cumDeltaClass}">CΔ: ${candle.cumulativeDelta}</span>
                    </div>
                `;
            });
            
            document.getElementById('testResults').innerHTML = html;
        }

        function runDailyResetTest() {
            // Create test data spanning multiple days
            const multiDayCandles = [
                { time: 1752558600, delta: 1000 }, // Day 1
                { time: 1752558900, delta: 500 },  // Day 1
                { time: 1752559200, delta: -300 }, // Day 1
                { time: 1752645000, delta: 800 },  // Day 2 (reset)
                { time: 1752645300, delta: -200 }, // Day 2
                { time: 1752731400, delta: 600 }   // Day 3 (reset)
            ];
            
            const results = calculateCumulativeDelta(multiDayCandles);
            
            let html = '<h3>Daily Reset Test Results:</h3>';
            let currentDay = null;
            
            results.forEach((candle, index) => {
                if (currentDay !== candle.tradingDay) {
                    currentDay = candle.tradingDay;
                    html += `<div style="margin-top: 15px; font-weight: bold; color: #ffa500;">--- ${candle.tradingDay} ---</div>`;
                }
                
                const deltaClass = candle.delta >= 0 ? 'positive' : 'negative';
                const cumDeltaClass = candle.cumulativeDelta >= 0 ? 'positive' : 'negative';
                
                html += `
                    <div class="data-row">
                        <span>Candle ${index + 1}</span>
                        <span class="${deltaClass}">Δ: ${candle.delta}</span>
                        <span class="${cumDeltaClass}">CΔ: ${candle.cumulativeDelta}</span>
                    </div>
                `;
            });
            
            document.getElementById('dailyResetResults').innerHTML = html;
        }
    </script>
</body>
</html>
