// --- High Performance Footprint Chart Plugin ---
import { positionsLine, positionsBox, drawRounded<PERSON>andle, isValidOHLCData } from './chart-utils.js';

/**
 * High-performance footprint renderer with optimized drawing and text caching
 */
class FootprintRenderer {
    constructor() {
        this._data = null;
        this._options = null;
        this._textCache = new Map();
        this._font10 = '10px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
        this._font9 = '9px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
        this._maxCacheSize = 200; // Increased cache size for better performance
        this._cumulativeDeltaCache = new Map(); // Cache for cumulative delta calculations
    }

    draw(target, priceToCoordinate) {
        target.useBitmapCoordinateSpace(scope => this._drawImpl(scope, priceToCoordinate));
    }

    update(data, options) {
        this._data = data;
        this._options = options;

        // Calculate cumulative delta for the data if bars are present and don't already have cumulative delta
        if (data?.bars?.length) {
            // Check if cumulative delta is already calculated
            const hasExistingCumulativeDelta = data.bars.some(bar =>
                bar.originalData && typeof bar.originalData.cumulativeDelta === 'number'
            );

            if (!hasExistingCumulativeDelta) {
                this._calculateCumulativeDelta(data.bars);
            }
        }

        // Manage cache size for memory efficiency - only clear when cache gets too large
        // to prevent unnecessary clearing that causes blinking
        if (this._textCache.size > this._maxCacheSize) {
            // Clear only half the cache instead of clearing everything
            const entries = Array.from(this._textCache.entries());
            const toKeep = entries.slice(-Math.floor(this._maxCacheSize / 2));
            this._textCache.clear();
            toKeep.forEach(([key, value]) => this._textCache.set(key, value));
        }
    }

    _drawImpl(scope, priceToCoordinate) {
        const d = this._data;
        if (!d?.bars?.length || !d.visibleRange || !this._options) return;
        
        const ctx = scope.context;
        const { from, to } = d.visibleRange;
        const barSpacing = d.barSpacing;
        
        // Use simple candlesticks for very small spacing
        if (barSpacing < 6) {
            this._drawCandles(ctx, priceToCoordinate, scope, from, to);
            return;
        }
        
        ctx.save();
        ctx.imageSmoothingEnabled = false;
        
        try {
            for (let i = from; i < to; ++i) {
                const bar = d.bars[i];
                if (bar?.originalData && isValidOHLCData(bar.originalData)) {
                    this._drawBar(ctx, bar, priceToCoordinate, scope, barSpacing);
                }
            }
        } finally {
            ctx.restore();
        }
    }

    _drawCandles(ctx, priceToCoordinate, scope, from, to) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        for (let i = from; i < to; ++i) {
            const bar = this._data.bars[i];
            if (!bar?.originalData || !isValidOHLCData(bar.originalData)) continue;
            
            const d = bar.originalData;
            const x = bar.x;
            const openY = priceToCoordinate(d.open);
            const closeY = priceToCoordinate(d.close);
            const highY = priceToCoordinate(d.high);
            const lowY = priceToCoordinate(d.low);
            
            if ([openY, closeY, highY, lowY].some(Number.isNaN)) continue;
            
            const isUp = d.close >= d.open;
            ctx.fillStyle = isUp ? this._options.upColor : this._options.downColor;
            ctx.fillRect(Math.round(x * h), Math.min(openY, closeY) * v, 1, Math.abs(closeY - openY) * v || 1);
            
            if (this._options.wickVisible) {
                ctx.fillStyle = isUp ? this._options.wickUpColor : this._options.wickDownColor;
                ctx.fillRect(Math.round(x * h), Math.min(highY, lowY) * v, 1, Math.abs(lowY - highY) * v || 1);
            }
        }
    }

    _drawBar(ctx, bar, priceToCoordinate, scope, barSpacing) {
        const { originalData: d, x } = bar;
        const bodyW = Math.max(2, Math.min(12, barSpacing * 0.8));
        
        this._drawCandle(ctx, d, x, bodyW, priceToCoordinate, scope);
        
        if (barSpacing >= 18 && Array.isArray(d.footprint) && d.footprint.length) {
            const fpW = this._footprintWidth(ctx, d.footprint);
            this._drawCells(ctx, d, x + bodyW / 2 + 4, fpW, priceToCoordinate, scope);
        }
    }

    _drawCandle(ctx, d, x, w, priceToCoordinate, scope) {
        drawRoundedCandle(ctx, d, x, w, priceToCoordinate, scope, this._options);
    }

    _footprintWidth(ctx, footprint) {
        const key = footprint.map(f => `${f.buyVolume}x${f.sellVolume}`).join('|');
        if (this._textCache.has(key)) return this._textCache.get(key);
        
        ctx.font = this._font10;
        let maxW = 0;
        
        for (const f of footprint) {
            const txt = this._formatText(f.buyVolume, f.sellVolume, f.buyVolume - f.sellVolume);
            maxW = Math.max(maxW, ctx.measureText(txt).width);
        }
        
        const fpW = Math.max(36, Math.ceil(maxW) + 10);
        this._textCache.set(key, fpW);
        return fpW;
    }

    _drawCells(ctx, d, startX, width, priceToCoordinate, scope) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        const fp = Array.isArray(d.footprint) ? [...d.footprint].sort((a, b) => b.priceLevel - a.priceLevel) : [];
        if (!fp.length) return;
        const highY = priceToCoordinate(d.high);
        const lowY = priceToCoordinate(d.low);
        const range = Math.abs(lowY - highY);
        const levels = fp.length;
        const cellH = range / levels;

        ctx.font = this._font9;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Gradient color stops
        const downGradient = this._options.downGradientColors || [
            '#F08090', '#D94A5A', '#A12A3A', '#3B0D16'
        ];
        const upGradient = this._options.upGradientColors || [
            '#5FE1C5', '#2FC1A2', '#007C65', '#00332A'
        ];
        const neutralColor = this._options.neutralColor || '#B2B5BE';

        const volumes = fp.map(f => (f.buyVolume || 0) + (f.sellVolume || 0));
        const maxVol = Math.max(...volumes);
        const minVol = Math.min(...volumes);
        const volRange = maxVol - minVol || 1;

        const cellGeometry = new Map();
        const xPosStatic = positionsLine(startX + width / 2, h, width);

        for (let i = 0; i < levels; ++i) {
            const f = fp[i];
            const cellTop = Math.round((highY + i * cellH) * v) / v;
            const cellBottom = Math.round((highY + (i + 1) * cellH) * v) / v;
            if ([cellTop, cellBottom].some(Number.isNaN)) continue;
            const buy = f.buyVolume || 0;
            const sell = f.sellVolume || 0;
            const total = buy + sell;
            const delta = buy - sell;
            const xPos = xPosStatic;
            const yPos = positionsBox(cellTop, cellBottom, v);
            let bg, alpha;
            if (!total) {
                bg = neutralColor;
                alpha = 0.08;
            } else {
                const ratio = (total - minVol) / volRange;
                const grad = delta >= 0 ? upGradient : downGradient;
                const idx = Math.min(grad.length - 1, Math.floor(ratio * grad.length));
                bg = grad[idx];
                alpha = Math.min(0.7, 0.25 + Math.min(1, Math.abs(delta / (total || 1))) * 0.45);
            }
            ctx.globalAlpha = alpha;
            ctx.fillStyle = bg;
            const radius = Math.min(2, Math.min(xPos.length, yPos.length) * 0.1);
            if (radius > 0.5 && (i === 0 || i === levels - 1)) {
                ctx.beginPath();
                if (i === 0) {
                    ctx.roundRect(xPos.position, yPos.position, xPos.length, yPos.length, [radius, radius, 0, 0]);
                } else {
                    ctx.roundRect(xPos.position, yPos.position, xPos.length, yPos.length, [0, 0, radius, radius]);
                }
                ctx.fill();
            } else {
                ctx.fillRect(xPos.position, yPos.position, xPos.length, yPos.length);
            }
            if (xPos.length >= 18 && yPos.length >= 8) {
                ctx.globalAlpha = 1;
                ctx.fillStyle = this._options.textColor || '#fff';
                ctx.fillText(
                    this._formatText(buy, sell, delta), 
                    xPos.position + xPos.length / 2, 
                    yPos.position + yPos.length / 2
                );
            }
            cellGeometry.set(f.priceLevel, {
                top: yPos.position,
                bottom: yPos.position + yPos.length,
                center: yPos.position + yPos.length / 2,
                height: yPos.length
            });
        }
        if (this._options.showImbalance !== false) {
            this._drawImbalanceIndicators(ctx, fp, startX, width, priceToCoordinate, scope, cellGeometry, xPosStatic);
        }
        // Remove cumulative delta calculation and rendering
        if (this._options.showTable !== false && (d.volume || d.delta !== undefined)) {
            this._drawSummaryTable(ctx, d, startX, width, lowY, scope);
        }
    }

    _drawImbalanceIndicators(ctx, fp, startX, width, priceToCoordinate, scope, cellGeometry, xPosStatic) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        const imbalanceThreshold = this._options.imbalanceThreshold || 300;
        const lineLength = 6;
        if (fp.length < 2) return;

        const sortedFp = [...fp].sort((a, b) => a.priceLevel - b.priceLevel);

        ctx.strokeStyle = this._options.imbalanceColor || '#FFB433';
        ctx.lineWidth = 1; // finer line for subtlety
        ctx.globalAlpha = 0.9;

        // Precompute left/right x positions tight to footprint block
        const rightX = (xPosStatic.position + xPosStatic.length + 1);
        const leftX = (xPosStatic.position - 2);

        // Buy imbalance markers (right side)
        for (let i = 1; i < sortedFp.length; i++) {
            if (i - 1 === 0) continue; // skip very bottom baseline check per original logic
            const cur = sortedFp[i];
            const lower = sortedFp[i - 1];
            const curBuy = cur.buyVolume || 0;
            const lowerSell = lower.sellVolume || 0;
            if (this._detectImbalance(curBuy, lowerSell, imbalanceThreshold)) {
                const geom = cellGeometry.get(cur.priceLevel);
                if (geom) {
                    ctx.beginPath();
                    ctx.moveTo(rightX * h, geom.center * v - (lineLength / 2) * v);
                    ctx.lineTo(rightX * h, geom.center * v + (lineLength / 2) * v);
                    ctx.stroke();
                }
            }
        }

        // Sell imbalance markers (left side)
        for (let i = 0; i < sortedFp.length - 1; i++) {
            const cur = sortedFp[i];
            const higher = sortedFp[i + 1];
            const curSell = cur.sellVolume || 0;
            const higherBuy = higher.buyVolume || 0;
            if (this._detectImbalance(curSell, higherBuy, imbalanceThreshold)) {
                const geom = cellGeometry.get(cur.priceLevel);
                if (geom) {
                    ctx.beginPath();
                    ctx.moveTo(leftX * h, geom.center * v - (lineLength / 2) * v);
                    ctx.lineTo(leftX * h, geom.center * v + (lineLength / 2) * v);
                    ctx.stroke();
                }
            }
        }

        ctx.globalAlpha = 1;
    }

    _detectImbalance(largerVolume, smallerVolume, thresholdPercent) {
        if (smallerVolume === 0) return largerVolume > 0;
        return largerVolume >= (thresholdPercent / 100) * smallerVolume;
    }

    /**
     * Calculate cumulative delta for bars on a daily basis
     * Resets cumulative delta at the start of each trading day
     */
    _calculateCumulativeDelta(bars) {
        if (!Array.isArray(bars) || bars.length === 0) {
            return;
        }

        let cumulativeDelta = 0;
        let currentDay = null;

        // Sort bars by time to ensure proper chronological order
        const sortedBars = [...bars].sort((a, b) => (a.originalData?.time || 0) - (b.originalData?.time || 0));

        for (const bar of sortedBars) {
            if (!bar.originalData) continue;

            const barTime = bar.originalData.time;
            if (!barTime) continue;

            // Convert timestamp to date (assuming timestamp is in seconds)
            const candleDate = new Date(barTime * 1000);
            const dayKey = candleDate.toDateString(); // Get day as string (e.g., "Mon Jan 01 2024")

            // Reset cumulative delta at start of new trading day
            if (currentDay !== dayKey) {
                currentDay = dayKey;
                cumulativeDelta = 0;
            }

            // Add current bar's delta to cumulative
            const barDelta = bar.originalData.delta || 0;
            cumulativeDelta += barDelta;

            // Store cumulative delta in the bar's original data
            bar.originalData.cumulativeDelta = cumulativeDelta;
        }
    }

    _drawSummaryTable(ctx, d, startX, width, lowY, scope) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        const showCumulativeDelta = this._options.showCumulativeDelta !== false;
        const numLines = showCumulativeDelta ? 3 : 2;
        const tableHeight = showCumulativeDelta ? 50 : 34; // Dynamic height based on content
        const tableY = lowY + 10;

        ctx.globalAlpha = 0.9;
        ctx.fillStyle = this._options.tableBackgroundColor || '#1E222D';
        const xPos = positionsLine(startX + width / 2, h, width);
        const yPos = positionsBox(tableY, tableY + tableHeight, v);
        const radius = 3;
        ctx.beginPath();
        ctx.roundRect(xPos.position, yPos.position, xPos.length, yPos.length, radius);
        ctx.fill();
        ctx.globalAlpha = 1;
        ctx.strokeStyle = this._options.tableBorderColor || '#2A2E39';
        ctx.lineWidth = 1;
        ctx.stroke();
        ctx.font = this._font9;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        const centerX = xPos.position + xPos.length / 2;
        const lineHeight = (yPos.length - (numLines === 3 ? 15 : 10)) / numLines;
        const startY = yPos.position + (numLines === 3 ? 7 : 5) + lineHeight / 2;

        // Volume line
        ctx.fillStyle = this._options.textColor || '#fff';
        ctx.fillText(`Vol: ${this._formatNum(d.volume || 0)}`, centerX, startY);

        // Delta line
        const delta = d.delta || 0;
        ctx.fillStyle = delta >= 0 ? this._options.upColor : this._options.downColor;
        ctx.fillText(`Δ: ${delta >= 0 ? '+' : ''}${this._formatNum(delta)}`, centerX, startY + lineHeight);

        // Cumulative Delta line (conditional)
        if (showCumulativeDelta) {
            const cumulativeDelta = d.cumulativeDelta || 0;
            ctx.fillStyle = cumulativeDelta >= 0 ? this._options.upColor : this._options.downColor;
            ctx.fillText(`CΔ: ${cumulativeDelta >= 0 ? '+' : ''}${this._formatNum(cumulativeDelta)}`, centerX, startY + lineHeight * 2);
        }

        ctx.globalAlpha = 1;
    }

    _formatNum(n) {
        if (n < 1000) return n.toString();
        if (n >= 1e6) return (n / 1e6).toFixed(1) + 'M';
        return (n / 1e3).toFixed(1) + 'K';
    }

    _formatText(buy, sell, delta) {
        switch (this._options.volumeDisplayMode) {
            case 'split': return `${this._formatNum(sell)}x${this._formatNum(buy)}`;
            case 'delta': return delta >= 0 ? `+${this._formatNum(delta)}` : `${this._formatNum(delta)}`;
            case 'total': return this._formatNum(buy + sell);
            default: return `${this._formatNum(sell)}x${this._formatNum(buy)}`;
        }
    }
}

/**
 * Optimized footprint series with proper OHLC support for MagnetOHLC
 * Fixed to create separate renderer instances per chart to prevent blinking in multi-chart layouts
 */
export const FootprintSeries = {
    // Create a new instance for each chart instead of sharing
    create() {
        return Object.create(this, {
            _renderer: { value: null, writable: true },
            _options: { value: null, writable: true },
            _defaultOptions: { value: null, writable: true }
        });
    },
    
    renderer() {
        if (!this._renderer) {
            this._renderer = new FootprintRenderer();
        }
        return this._renderer;
    },
    
    update(data, options) {
        if (!this._renderer) {
            this._renderer = new FootprintRenderer();
        }
        this._options = this._options ? Object.assign(this._options, options) : Object.assign({}, this.defaultOptions(), options);
        this._renderer.update(data, this._options);
    },

    setData(data) {
        if (!this._renderer) {
            this._renderer = new FootprintRenderer();
        }

        // Store the raw data for cumulative delta calculation
        this._rawData = Array.isArray(data) ? data : [];

        // Calculate cumulative delta directly on the raw data
        if (this._rawData.length > 0) {
            this._calculateCumulativeDeltaForRawData(this._rawData);
        }

        // The chart engine will call update() later with the proper data structure
        // We don't need to call renderer.update() here as the chart engine handles it
    },

    _calculateCumulativeDeltaForRawData(data) {
        if (!Array.isArray(data) || data.length === 0) {
            return;
        }

        let cumulativeDelta = 0;
        let currentDay = null;

        // Sort data by time to ensure proper chronological order
        const sortedData = [...data].sort((a, b) => (a.time || 0) - (b.time || 0));

        for (const item of sortedData) {
            if (!item.time) continue;

            // Convert timestamp to date (assuming timestamp is in seconds)
            const candleDate = new Date(item.time * 1000);
            const dayKey = candleDate.toDateString();

            // Reset cumulative delta at start of new trading day
            if (currentDay !== dayKey) {
                currentDay = dayKey;
                cumulativeDelta = 0;
            }

            // Add current item's delta to cumulative
            const itemDelta = item.delta || 0;
            cumulativeDelta += itemDelta;

            // Store cumulative delta in the item
            item.cumulativeDelta = cumulativeDelta;
        }
    },
    
    /**
     * Price value builder for MagnetOHLC - returns [open, high, low, close]
     * This is critical for proper crosshair magnetization to OHLC values
     */
    priceValueBuilder(row) {
        if (!isValidOHLCData(row)) {
            return [0, 0, 0, 0];
        }
        return [row.open, row.high, row.low, row.close];
    },
    
    isWhitespace(row) {
        return !isValidOHLCData(row);
    },
    
    defaultOptions() {
        if (!this._defaultOptions) {
            this._defaultOptions = {
                upColor: '#089981',
                downColor: '#F23645',
                borderUpColor: '#089981',
                borderDownColor: '#F23645',
                wickUpColor: '#089981',
                wickDownColor: '#F23645',
                borderVisible: true,
                wickVisible: true,
                neutralColor: '#B2B5BE',
                cellBorderColor: '#333',
                textColor: '#fff',
                volumeDisplayMode: 'split',
                visible: true,
                showTable: true,
                showCumulativeDelta: true, // New option to control cumulative delta display
                tableBackgroundColor: '#1E222D',
                tableBorderColor: '#2A2E39',
                showImbalance: true,
                imbalanceThreshold: 300,
                imbalanceColor: '#FFFF00'
            };
        }
        return this._defaultOptions;
    },
    
    applyOptions(options) {
        this._options = Object.assign({}, this._options || this.defaultOptions(), options);
        return this;
    },
    
    destroy() {
        if (this._renderer?._textCache) {
            this._renderer._textCache.clear();
        }
        if (this._renderer?._cumulativeDeltaCache) {
            this._renderer._cumulativeDeltaCache.clear();
        }
        this._renderer = null;
        this._options = null;
        this._defaultOptions = null;
    }
};
